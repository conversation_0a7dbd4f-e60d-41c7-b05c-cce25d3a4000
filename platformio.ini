; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:esp32-c3-supermini]
platform = espressif32
board = lolin_c3_mini
framework = arduino

; Serial monitor configuration
monitor_speed = 115200

build_flags =
  -D ARDUINO_USB_CDC_ON_BOOT=1
  -D ARDUINO_USB_MODE=1
; monitor_filters = esp32_exception_decoder

; ; Upload configuration for better reliability
; upload_speed = 115200
; ; upload_flags =
; ;     --before=default_reset
; ;     --after=hard_reset
; ;     --connect-attempts=20
; ;     --chip=esp32c3

; ; Build flags to ensure proper USB CDC support
; build_flags =
;     -D ARDUINO_USB_CDC_ON_BOOT=1
;     -D ARDUINO_USB_MODE=1
    -D CORE_DEBUG_LEVEL=3

; Additional upload configuration for ESP32-C3
upload_protocol = esptool
; upload_port = /dev/cu.usbmodem24101

lib_deps =
	fastled/FastLED@^3.6.0